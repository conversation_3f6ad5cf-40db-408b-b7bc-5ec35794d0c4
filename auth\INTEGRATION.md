# 🚀 Quick Integration Guide

This guide provides step-by-step instructions for integrating the auth module into your project.

## ✨ New Features Added

- **User Retrieval Endpoints**: GET /auth/users/:id and GET /auth/users
- **GraphQL User Queries**: `user(id: ID!)` and `users` queries
- **External Auth Service Integration**: Forward encrypted tokens to external auth service
- **Comprehensive Swagger Documentation**: Full API documentation for all endpoints

## 📋 Prerequisites

- NestJS backend project
- Next.js frontend project
- Node.js 18+ installed
- Access to external auth service

## 🔧 Backend Integration (5 minutes)

### 1. Copy Files
```bash
cp -r auth/backend/* your-backend-project/src/modules/auth/
```

### 2. Install Dependencies
```bash
npm install @nestjs/axios jsonwebtoken jwks-client @fastify/cookie rxjs
npm install @types/jsonwebtoken
```

### 3. Add Environment Variables
Create/update `.env.local`:
```bash
AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
CORS_ORIGINS=http://your-frontend-domain:3061
```

### 4. Import Module
```typescript
// app.module.ts
import { AuthModule } from './modules/auth/auth.module';

@Module({
  imports: [AuthModule],
})
export class AppModule {}
```

### 5. Add Cookie Support
```typescript
// main.ts
await app.register(require('@fastify/cookie'), {
  secret: process.env.COOKIE_SECRET || 'your-secret-key',
});
```

## 🎨 Frontend Integration (5 minutes)

### 1. Copy Files
```bash
cp -r auth/frontend/* your-frontend-project/src/auth/
```

### 2. Add Environment Variables
Create/update `.env.local`:
```bash
NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
NEXT_PUBLIC_CUSTOMER_API_URL=http://your-backend-domain:3060
NEXT_PUBLIC_AUTH_DEBUG_MODE=false
```

### 3. Wrap App with Provider
```typescript
// pages/_app.tsx
import { AuthProvider } from '../auth/frontend/components/AuthProvider';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <AuthProvider>
      <Component {...pageProps} />
    </AuthProvider>
  );
}
```

### 4. Use in Components
```typescript
import { useAuth } from '../auth/frontend/hooks/useAuth';

function MyComponent() {
  const { isAuthenticated, user, login, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <button onClick={() => login()}>Login</button>;
  }
  
  return (
    <div>
      <p>Welcome, {user?.email}!</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

## 🔒 Protect Routes

### Backend
```typescript
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from './modules/auth/auth.guard';

@Controller('protected')
export class ProtectedController {
  @Get()
  @UseGuards(AuthGuard)
  getProtectedData() {
    return { message: 'Protected data' };
  }
}
```

### Frontend
```typescript
import { ProtectedRoute } from '../auth/frontend/components/ProtectedRoute';

function AdminPage() {
  return (
    <ProtectedRoute>
      <div>Admin content</div>
    </ProtectedRoute>
  );
}
```

## ✅ Test Integration

### 1. Start Services
```bash
# Backend
npm run start:dev

# Frontend
npm run dev
```

### 2. Test Authentication
1. Visit your frontend application
2. Should redirect to auth service if not logged in
3. After login, should redirect back with cookies
4. Verify protected routes work

### 3. Check Endpoints
- `GET /auth/status` - Should return service status
- `GET /auth/me/cookies` - Should return user info (with auth)
- `GET /graphql` - Should show GraphQL playground

## 🐛 Troubleshooting

### Common Issues

1. **Environment variable errors**
   - Ensure all required variables are set
   - Check variable names match exactly

2. **CORS errors**
   - Add your frontend domain to `CORS_ORIGINS`
   - Verify domains match exactly

3. **Cookie issues**
   - Check browser developer tools
   - Verify cookies are being set
   - Check domain configuration

4. **Authentication loops**
   - Set `NEXT_PUBLIC_AUTH_DEBUG_MODE=true` for debugging
   - Check console for error messages

### Debug Mode

Enable debug mode for development:
```bash
NEXT_PUBLIC_AUTH_DEBUG_MODE=true
```

This will show authentication status and provide manual login buttons instead of auto-redirecting.

## 📞 Support

If you encounter issues:
1. Check this integration guide
2. Review the main README.md
3. Enable debug mode
4. Check browser console for errors
5. Verify environment variables are correct

## 🎯 Next Steps

After successful integration:
1. Customize user interface
2. Add role-based permissions
3. Configure production environment variables
4. Set up monitoring and logging
5. Test with production auth service

## 📝 Checklist

- [ ] Backend files copied
- [ ] Backend dependencies installed
- [ ] Backend environment variables configured
- [ ] AuthModule imported
- [ ] Cookie support added
- [ ] Frontend files copied
- [ ] Frontend environment variables configured
- [ ] AuthProvider added to app
- [ ] Authentication tested
- [ ] Protected routes working
- [ ] Debug mode disabled for production
