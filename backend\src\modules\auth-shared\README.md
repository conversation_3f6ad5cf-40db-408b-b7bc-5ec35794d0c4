# Auth-Shared Module

A reusable authentication module that integrates with external auth service at `ng-auth-dev.dev1.ngnair.com`. This module provides both REST API endpoints and GraphQL resolvers for authentication operations.

## Features

- **External Auth Service Integration**: Seamlessly integrates with external authentication service
- **Cookie-based Authentication**: Uses encrypted access_token and refresh_token cookies
- **JWT Verification**: Verifies JWT tokens using JWKS endpoint
- **AES-256-GCM Encryption**: Encrypts/decrypts authentication tokens
- **REST API Endpoints**: Complete set of authentication endpoints
- **GraphQL Resolvers**: GraphQL queries and mutations for authentication
- **Copy-Paste Ready**: Designed for easy integration across microservices

## REST API Endpoints

### Authentication Endpoints

#### 1. Get Current User (`/auth/me`)
- **Method**: `GET`
- **Description**: Decrypts and decodes access_token from cookie to get current session user
- **Authentication**: Requires `access_token` cookie
- **Response**: User object with id, email, first_name, last_name, etc.

```bash
GET /auth/me
Cookie: access_token=<encrypted_token>
```

#### 2. Get All Users (`/auth/users`)
- **Method**: `GET`
- **Description**: Fetches all users from external auth server
- **Authentication**: Requires `access_token` cookie
- **Response**: Array of user objects

```bash
GET /auth/users
Cookie: access_token=<encrypted_token>
```

#### 3. Get User by ID (`/auth/users/{id}`)
- **Method**: `GET`
- **Description**: Fetches specific user info by ID from external auth server
- **Authentication**: Requires `access_token` cookie
- **Parameters**: `id` (UUID) - User unique identifier
- **Response**: User object

```bash
GET /auth/users/086ef523-157d-4a56-ba2c-1afe2427740d
Cookie: access_token=<encrypted_token>
```

## GraphQL Queries

### Authentication Queries

#### 1. Current User Query (`me`)
```graphql
query GetCurrentUser {
  me {
    id
    email
    firstName
    lastName
    role
    permissions
    createdAt
    updatedAt
  }
}
```

#### 2. All Users Query (`users`)
```graphql
query GetAllUsers {
  users {
    id
    email
    firstName
    lastName
    role
    permissions
    createdAt
    updatedAt
  }
}
```

#### 3. User by ID Query (`user`)
```graphql
query GetUserById($id: String!) {
  user(id: $id) {
    id
    email
    firstName
    lastName
    role
    permissions
    createdAt
    updatedAt
  }
}
```

## Integration Guide

### Backend Integration

1. **Install the module in your NestJS application:**

```typescript
import { Module } from '@nestjs/common';
import { AuthSharedModule } from './modules/auth-shared/auth-shared.module';

@Module({
  imports: [
    AuthSharedModule, // Add this line
    // ... other modules
  ],
})
export class AppModule {}
```

2. **Environment Variables Required:**

```env
# External Auth Service Configuration
AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=your-32-character-encryption-key

# Cookie Configuration
COOKIE_DOMAIN=.dev1.ngnair.com
ALLOWED_ORIGINS=https://ng-customer-fe-dev.dev1.ngnair.com,https://ng-customer-admin-dev.dev1.ngnair.com
```

3. **Using Authentication Guards:**

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthSharedGuard, getCurrentUser, User } from './modules/auth-shared';

@Controller('protected')
export class ProtectedController {
  
  @Get('data')
  @UseGuards(AuthSharedGuard)
  async getProtectedData(@getCurrentUser() user: User) {
    return { message: `Hello ${user.email}` };
  }
}
```

### Frontend Integration

1. **Copy the auth folder to your frontend project:**

```bash
cp -r backend/src/modules/auth-shared/frontend-auth frontend/src/auth
```

2. **Environment Variables for Frontend:**

```env
NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=your-32-character-encryption-key
NEXT_PUBLIC_COOKIE_DOMAIN=.dev1.ngnair.com
```

## Authentication Flow

1. **User Authentication**: User logs in through external auth service
2. **Cookie Setting**: External service sets encrypted access_token and refresh_token cookies
3. **API Requests**: Frontend sends requests with cookies automatically included
4. **Token Extraction**: Backend extracts encrypted tokens from cookies
5. **External Service Calls**: For `/auth/users` and `/auth/users/{id}`, forward encrypted token to external service
6. **Local Decryption**: For `/auth/me`, decrypt and decode token locally to get user data

## Module Structure

```
auth-shared/
├── auth-shared.module.ts     # Main module configuration
├── auth.controller.ts        # REST API endpoints
├── auth.service.ts          # Core authentication logic
├── auth.guard.ts            # Authentication guard
├── auth.middleware.ts       # Authentication middleware
├── decorators/              # Custom decorators
├── dto/                     # Data transfer objects
├── guards/                  # Additional guards (API, Admin)
├── types/                   # TypeScript type definitions
└── index.ts                # Module exports
```

## Error Handling

The module provides comprehensive error handling:

- **401 Unauthorized**: Invalid or expired access token
- **404 Not Found**: User not found (for user by ID endpoint)
- **500 Internal Server Error**: External service communication errors

## Security Features

- **AES-256-GCM Encryption**: All tokens are encrypted before storage
- **JWT Verification**: Tokens are verified using JWKS endpoint
- **Cookie Security**: Secure, HttpOnly, SameSite cookies
- **CORS Protection**: Configurable allowed origins
- **Request Validation**: Input validation and sanitization

## Testing

The module includes comprehensive logging for debugging:

- Cookie extraction and validation
- External service communication
- Token encryption/decryption
- JWT verification steps
- Error handling and reporting

## Support

For issues or questions regarding this auth-shared module:
1. Check the logs for detailed error information
2. Verify environment variables are correctly set
3. Ensure external auth service is accessible
4. Validate cookie domain configuration
